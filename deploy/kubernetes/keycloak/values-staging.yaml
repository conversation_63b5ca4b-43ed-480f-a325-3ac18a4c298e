# Staging environment values for Keycloak
# This file contains staging-specific overrides

keycloak:
  # Staging admin credentials (should use existing secrets)
  auth:
    adminUser: admin
    existingSecret: "keycloak-admin-secret"
    passwordSecretKey: "admin-password"
  
  # Staging ingress configuration
  ingress:
    enabled: true
    hostname: auth-staging.tourdecloud.com
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
      cert-manager.io/cluster-issuer: "letsencrypt-staging"
    tls: true
    extraTls:
      - hosts:
          - auth-staging.tourdecloud.com
        secretName: keycloak-staging-tls-secret
  
  # Staging resources (between dev and prod)
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi
  
  # Two replicas for staging
  replicaCount: 2
  
  # Enable pod disruption budget
  pdb:
    create: true
    minAvailable: 1
  
  # Staging persistence
  persistence:
    enabled: true
    size: 10Gi
    storageClass: "standard"
  
  # PostgreSQL configuration for staging
  postgresql:
    enabled: true
    auth:
      existingSecret: "keycloak-postgresql-secret"
      secretKeys:
        adminPasswordKey: "postgres-password"
        userPasswordKey: "user-password"
    architecture: standalone
    primary:
      persistence:
        enabled: true
        size: 20Gi
        storageClass: "standard"
      resources:
        limits:
          cpu: 500m
          memory: 512Mi
        requests:
          cpu: 250m
          memory: 256Mi
  
  # Staging-specific environment variables
  extraEnvVars:
    - name: KEYCLOAK_LOG_LEVEL
      value: "INFO"
    - name: KC_LOG_LEVEL
      value: "INFO"
    - name: KC_HOSTNAME_STRICT
      value: "true"
    - name: KC_HOSTNAME_STRICT_HTTPS
      value: "true"
    - name: KC_HTTP_ENABLED
      value: "false"
    - name: KC_PROXY
      value: "edge"
    - name: KC_HEALTH_ENABLED
      value: "true"
    - name: KC_METRICS_ENABLED
      value: "true"
  
  # Staging probes
  livenessProbe:
    initialDelaySeconds: 240
    periodSeconds: 20
    timeoutSeconds: 5
    failureThreshold: 3
  
  readinessProbe:
    initialDelaySeconds: 45
    periodSeconds: 10
    timeoutSeconds: 3
    failureThreshold: 3
  
  # Security context for staging
  podSecurityContext:
    enabled: true
    fsGroup: 1001
    runAsNonRoot: true
  
  containerSecurityContext:
    enabled: true
    runAsUser: 1001
    runAsNonRoot: true
    readOnlyRootFilesystem: false
    allowPrivilegeEscalation: false
    capabilities:
      drop:
        - ALL
  
  # Node affinity for staging workloads
  nodeAffinityPreset:
    type: "soft"
    key: "node-type"
    values:
      - "staging"
  
  # Pod anti-affinity for availability
  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
        - weight: 50
          podAffinityTerm:
            labelSelector:
              matchLabels:
                app.kubernetes.io/name: keycloak
                app.kubernetes.io/instance: tourdecloud-keycloak
            topologyKey: kubernetes.io/hostname
  
  # Staging annotations
  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"
    prometheus.io/path: "/metrics"

# Staging custom configuration
customConfig:
  realms:
    tourdecloud-staging:
      realm: "tourdecloud-staging"
      displayName: "Tour de Cloud Staging"
      enabled: true
      sslRequired: "external"
      registrationAllowed: false
      loginWithEmailAllowed: true
      duplicateEmailsAllowed: false
      resetPasswordAllowed: true
      editUsernameAllowed: false
      bruteForceProtected: true
      permanentLockout: false
      maxFailureWaitSeconds: 600
      minimumQuickLoginWaitSeconds: 60
      waitIncrementSeconds: 60
      quickLoginCheckMilliSeconds: 1000
      maxDeltaTimeSeconds: 43200
      failureFactor: 15
      clients:
        - clientId: "tourdecloud-app"
          name: "Tour de Cloud Application"
          description: "Main application client for Tour de Cloud Staging"
          enabled: true
          clientAuthenticatorType: "client-secret"
          secret: "${KEYCLOAK_CLIENT_SECRET}"
          redirectUris:
            - "https://app-staging.tourdecloud.com/*"
            - "https://auth-staging.tourdecloud.com/*"
          webOrigins:
            - "https://app-staging.tourdecloud.com"
            - "https://auth-staging.tourdecloud.com"
          standardFlowEnabled: true
          implicitFlowEnabled: false
          directAccessGrantsEnabled: false
          serviceAccountsEnabled: true
          publicClient: false
          frontchannelLogout: true
          protocol: "openid-connect"
          attributes:
            "access.token.lifespan": "600"
            "sso.session.idle.timeout": "3600"
            "sso.session.max.lifespan": "43200"
            "client.session.idle.timeout": "3600"
            "client.session.max.lifespan": "43200"
      roles:
        realm:
          - name: "user"
            description: "Standard user role"
          - name: "verified-user"
            description: "Verified user role with additional privileges"
          - name: "maintainer"
            description: "Maintainer role with project management privileges"
          - name: "admin"
            description: "Administrator role"
          - name: "tester"
            description: "Tester role for staging"
        client:
          tourdecloud-app:
            - name: "app-user"
              description: "Application user"
            - name: "app-admin"
              description: "Application administrator"
            - name: "app-tester"
              description: "Application tester"

# Staging monitoring
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    namespace: "monitoring"
    interval: "30s"
    scrapeTimeout: "10s"
    labels:
      app: "keycloak"
      environment: "staging"

# Staging backup
backup:
  enabled: true
  schedule: "0 3 * * *"
  retention: "14d"
  storageClass: "standard"
  resources:
    limits:
      cpu: 250m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 128Mi
