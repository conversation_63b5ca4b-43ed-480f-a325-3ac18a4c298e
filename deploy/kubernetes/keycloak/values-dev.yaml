# Development environment values for Keycloak
# This file contains development-specific overrides

keycloak:
  # Development admin credentials (use stronger passwords in production)
  auth:
    adminUser: admin
    adminPassword: "dev-admin-password"
  
  # Development ingress configuration (disabled for port-forward access)
  ingress:
    enabled: false
    hostname: keycloak-dev.local
    annotations:
      nginx.ingress.kubernetes.io/rewrite-target: /
      nginx.ingress.kubernetes.io/ssl-redirect: "false"
    tls: false
  
  # Reduced resources for development
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  
  # Single replica for development
  replicaCount: 1
  
  # Development persistence (smaller volumes)
  persistence:
    enabled: true
    size: 2Gi
  
  # PostgreSQL configuration for development
  postgresql:
    enabled: true
    auth:
      postgresPassword: "dev-postgres-password"
      password: "dev-keycloak-password"
    primary:
      persistence:
        enabled: true
        size: 2Gi
      resources:
        limits:
          cpu: 250m
          memory: 256Mi
        requests:
          cpu: 100m
          memory: 128Mi
  
  # Development-specific environment variables
  extraEnvVars:
    - name: <PERSON>E<PERSON>CLOAK_LOG_LEVEL
      value: "DEBUG"
    - name: KC_LOG_LEVEL
      value: "DEBUG"
    - name: KC_HOSTNAME_STRICT
      value: "false"
    - name: KC_HOSTNAME_STRICT_HTTPS
      value: "false"
    - name: KC_HTTP_ENABLED
      value: "true"
    - name: KC_HOSTNAME_URL
      value: "http://localhost:8080"
    - name: KC_HOSTNAME_ADMIN_URL
      value: "http://localhost:8080"
  
  # Faster startup for development
  livenessProbe:
    initialDelaySeconds: 120
    periodSeconds: 10
  
  readinessProbe:
    initialDelaySeconds: 30
    periodSeconds: 5

# Development custom configuration
customConfig:
  realms:
    tourdecloud-dev:
      realm: "tourdecloud-dev"
      displayName: "Tour de Cloud Development"
      enabled: true
      sslRequired: "external"
      registrationAllowed: true
      loginWithEmailAllowed: true
      duplicateEmailsAllowed: false
      resetPasswordAllowed: true
      editUsernameAllowed: false
      bruteForceProtected: true
      clients:
        - clientId: "tourdecloud-app"
          name: "Tour de Cloud Application"
          description: "Main application client for Tour de Cloud"
          enabled: true
          clientAuthenticatorType: "client-secret"
          redirectUris:
            - "http://localhost:3000/*"
            - "http://localhost:8080/*"
            - "http://keycloak-dev.local/*"
          webOrigins:
            - "http://localhost:3000"
            - "http://localhost:8080"
            - "http://keycloak-dev.local"
          standardFlowEnabled: true
          implicitFlowEnabled: false
          directAccessGrantsEnabled: true
          serviceAccountsEnabled: false
          publicClient: true
          frontchannelLogout: true
          protocol: "openid-connect"
          attributes:
            "saml.assertion.signature": "false"
            "saml.force.post.binding": "false"
            "saml.multivalued.roles": "false"
            "saml.encrypt": "false"
            "saml.server.signature": "false"
            "saml.server.signature.keyinfo.ext": "false"
            "exclude.session.state.from.auth.response": "false"
            "saml_force_name_id_format": "false"
            "saml.client.signature": "false"
            "tls.client.certificate.bound.access.tokens": "false"
            "saml.authnstatement": "false"
            "display.on.consent.screen": "false"
            "saml.onetimeuse.condition": "false"
      users:
        - username: "testuser"
          email: "<EMAIL>"
          firstName: "Test"
          lastName: "User"
          enabled: true
          emailVerified: true
          credentials:
            - type: "password"
              value: "testpassword"
              temporary: false
          realmRoles:
            - "user"
          clientRoles:
            tourdecloud-app:
              - "app-user"
      roles:
        realm:
          - name: "user"
            description: "Standard user role"
          - name: "verified-user"
            description: "Verified user role with additional privileges"
          - name: "maintainer"
            description: "Maintainer role with project management privileges"
          - name: "admin"
            description: "Administrator role"
        client:
          tourdecloud-app:
            - name: "app-user"
              description: "Application user"
            - name: "app-admin"
              description: "Application administrator"

# Development monitoring (disabled for simplicity)
monitoring:
  enabled: false
